import { NextRequest, NextResponse } from 'next/server';
import { TranslationRequest, TranslationResponse } from '@/types/translation';
import { hasGlossarySupport } from '@/lib/languages';

export async function POST(request: NextRequest) {
  try {
    // Debug environment variables
    console.log('Environment check:', {
      hasDeeplKey: !!process.env.DEEPL_API_KEY,
      keyLength: process.env.DEEPL_API_KEY?.length || 0,
      keyPreview: process.env.DEEPL_API_KEY ? process.env.DEEPL_API_KEY.substring(0, 8) + '...' : 'undefined',
      hasGlossaryId: !!process.env.DEEPL_GLOSSARY_ID
    });

    // Check if DeepL API key is configured
    if (!process.env.DEEPL_API_KEY) {
      console.error('DEEPL_API_KEY environment variable is not set');
      return NextResponse.json(
        {
          error: 'Configuration error',
          message: 'Translation service is not properly configured - API key missing',
          debug: {
            hasKey: false,
            envKeys: Object.keys(process.env).filter(key => key.includes('DEEPL'))
          }
        },
        { status: 500 }
      );
    }

    const body: TranslationRequest = await request.json();
    const { text, source_lang, target_lang } = body;

    // Validate required fields
    if (!text || !target_lang) {
      return NextResponse.json(
        { error: 'Missing required fields', message: 'Text and target language are required' },
        { status: 400 }
      );
    }

    // Prepare form data for DeepL API
    const formData = new URLSearchParams();
    formData.append('text', text);
    formData.append('target_lang', target_lang);
    
    if (source_lang && source_lang !== 'auto') {
      formData.append('source_lang', source_lang);
    }
    
    // Add glossary ID if available and language pair is supported
    const glossaryId = process.env.DEEPL_GLOSSARY_ID;
    const hasGlossary = hasGlossarySupport(source_lang || 'EN', target_lang);

    if (glossaryId && hasGlossary) {
      formData.append('glossary_id', glossaryId);
    }

    // Make request to DeepL API
    const response = await fetch('https://api.deepl.com/v2/translate', {
      method: 'POST',
      headers: {
        'Authorization': `DeepL-Auth-Key ${process.env.DEEPL_API_KEY}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('DeepL API Error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
        apiKey: process.env.DEEPL_API_KEY ? 'Present' : 'Missing'
      });
      return NextResponse.json(
        { error: 'Translation failed', message: `DeepL API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const translationData: TranslationResponse = await response.json();

    // Add glossary information to the response
    const responseWithGlossaryInfo = {
      ...translationData,
      glossaryUsed: hasGlossary && !!glossaryId,
      glossaryMessage: hasGlossary ?
        (glossaryId ? 'Glossary applied' : 'Glossary available but not configured') :
        'No glossary pair available for this translation'
    };

    return NextResponse.json(responseWithGlossaryInfo);
  } catch (error) {
    console.error('Translation error:', error);
    return NextResponse.json(
      { error: 'Internal server error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
