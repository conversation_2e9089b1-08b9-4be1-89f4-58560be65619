import { TranslationHistoryItem } from '@/types/translation';

export const generateId = (): string => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

export const formatTimestamp = (date: Date): string => {
  const now = new Date();
  const diffInMs = now.getTime() - new Date(date).getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  } else if (diffInDays < 7) {
    return `${diffInDays}d ago`;
  } else {
    return new Date(date).toLocaleDateString();
  }
};

export const truncateText = (text: string, maxLength: number = 100): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

export const getLanguagePairKey = (sourceLang: string, targetLang: string): string => {
  return `${sourceLang}-${targetLang}`;
};

export const groupTranslationsByDate = (translations: TranslationHistoryItem[]) => {
  const groups: { [key: string]: TranslationHistoryItem[] } = {};
  
  translations.forEach(translation => {
    const date = new Date(translation.timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    let groupKey: string;
    
    if (date.toDateString() === today.toDateString()) {
      groupKey = 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      groupKey = 'Yesterday';
    } else {
      groupKey = date.toLocaleDateString();
    }
    
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(translation);
  });
  
  return groups;
};

export const exportToCSV = (translations: TranslationHistoryItem[]): string => {
  const headers = ['Date', 'Original Text', 'Translated Text', 'Source Language', 'Target Language', 'Detected Language'];

  const rows = translations.map(translation => {
    return [
      new Date(translation.timestamp).toLocaleString(),
      `"${translation.originalText.replace(/"/g, '""')}"`,
      `"${translation.translatedText.replace(/"/g, '""')}"`,
      translation.sourceLang,
      translation.targetLang,
      translation.detectedSourceLang || ''
    ];
  });

  return [headers, ...rows].map(row => row.join(',')).join('\n');
};

export const getTranslationStats = (translations: TranslationHistoryItem[]) => {
  const stats = {
    totalTranslations: translations.length,
    languagePairs: {} as { [key: string]: number },
    topLanguages: {} as { [key: string]: number },
    recentActivity: translations.slice(0, 5),
    translationsThisWeek: 0,
    translationsThisMonth: 0
  };

  const now = new Date();
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  translations.forEach(translation => {
    const translationDate = new Date(translation.timestamp);
    
    // Count language pairs
    const pairKey = getLanguagePairKey(translation.sourceLang, translation.targetLang);
    stats.languagePairs[pairKey] = (stats.languagePairs[pairKey] || 0) + 1;
    
    // Count individual languages
    stats.topLanguages[translation.sourceLang] = (stats.topLanguages[translation.sourceLang] || 0) + 1;
    stats.topLanguages[translation.targetLang] = (stats.topLanguages[translation.targetLang] || 0) + 1;
    
    // Count recent activity
    if (translationDate >= oneWeekAgo) {
      stats.translationsThisWeek++;
    }
    if (translationDate >= oneMonthAgo) {
      stats.translationsThisMonth++;
    }
  });

  return stats;
};

export const searchTranslations = (
  translations: TranslationHistoryItem[],
  query: string,
  filters?: {
    sourceLang?: string;
    targetLang?: string;
    dateFrom?: Date;
    dateTo?: Date;
  }
): TranslationHistoryItem[] => {
  let filtered = translations;

  // Text search
  if (query.trim()) {
    const lowercaseQuery = query.toLowerCase();
    filtered = filtered.filter(translation =>
      translation.originalText.toLowerCase().includes(lowercaseQuery) ||
      translation.translatedText.toLowerCase().includes(lowercaseQuery)
    );
  }

  // Apply filters
  if (filters) {
    if (filters.sourceLang) {
      filtered = filtered.filter(t => t.sourceLang === filters.sourceLang);
    }
    if (filters.targetLang) {
      filtered = filtered.filter(t => t.targetLang === filters.targetLang);
    }
    if (filters.dateFrom) {
      filtered = filtered.filter(t => new Date(t.timestamp) >= filters.dateFrom!);
    }
    if (filters.dateTo) {
      filtered = filtered.filter(t => new Date(t.timestamp) <= filters.dateTo!);
    }
  }

  return filtered;
};
