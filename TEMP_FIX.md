# TEMPORARY FIX - Hardcode API Key

If you can't set environment variables on your hosting, here's a temporary fix:

## Replace line 45 in src/app/api/translate/route.ts:

**From:**
```javascript
'Authorization': `DeepL-Auth-Key ${process.env.DEEPL_API_KEY}`,
```

**To:**
```javascript
'Authorization': `DeepL-Auth-Key 21678126-5014-407f-ac7e-583490719029`,
```

## And replace line 36-39:

**From:**
```javascript
const glossaryId = process.env.DEEPL_GLOSSARY_ID;
if (glossaryId) {
  formData.append('glossary_id', glossaryId);
}
```

**To:**
```javascript
formData.append('glossary_id', '0a52855b-5768-48e0-8800-5ed1fc91ee30');
```

## ⚠️ WARNING:
This is only for testing! Don't use this in production as it exposes your API key in the code.

## Better Solution:
Set up environment variables properly on your hosting provider.
