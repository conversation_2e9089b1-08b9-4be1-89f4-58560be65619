const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Deployment Helper Script');
console.log('============================');

// Check if build exists
if (!fs.existsSync('.next')) {
  console.log('❌ No build found. Running build first...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Build completed successfully!');
  } catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  }
} else {
  console.log('✅ Build found in .next directory');
}

// Check for environment variables
console.log('\n📋 Environment Variables Check:');
const envExample = fs.readFileSync('.env.example', 'utf8');
console.log('Required environment variables:');
console.log(envExample);

// Check if .env.local exists
if (fs.existsSync('.env.local')) {
  console.log('✅ .env.local file found');
} else {
  console.log('⚠️  .env.local file not found. Create it with your API keys.');
}

console.log('\n📦 Deployment Options:');
console.log('1. For Vercel/Netlify: Push to GitHub and connect your repo');
console.log('2. For Node.js server: Upload all files + .next folder');
console.log('3. For static hosting: Run static export first');

console.log('\n📖 See DEPLOYMENT.md for detailed instructions');
console.log('🌐 Test your API at: your-domain.com/api/test');
