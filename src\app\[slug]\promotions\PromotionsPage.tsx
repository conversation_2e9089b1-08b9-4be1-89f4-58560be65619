'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';

type Promotion = {
  id: number;
  title: string;
  description: string;
  image_url?: string;
  start_date?: string;
  end_date?: string;
  terms_conditions?: string;
};

export default function PromotionsPage() {
  const { slug } = useParams();
  const router = useRouter();
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const brand = Array.isArray(slug) ? slug[0].toLowerCase() : slug?.toLowerCase();

  useEffect(() => {
    if (!brand) return;

    const fetchPromotions = async () => {
      try {
        // Using the provided API endpoint
        const apiUrl = `https://knowledgebase.mistycasino12.com/${brand}-promotion`;
        const res = await fetch(apiUrl);
        
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        
        const result = await res.json();

        if (result.success && Array.isArray(result.data)) {
          setPromotions(result.data);
        } else {
          setError(true);
        }
      } catch (err) {
        console.error('Fetch error:', err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    fetchPromotions();
  }, [brand]);

  const handleBackToBrand = () => {
    router.push(`/${brand}`);
  };

  const handleBackToHome = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackToHome}
                className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer"
              >
                ← Home
              </button>
              <button
                onClick={handleBackToBrand}
                className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer"
              >
                ← Back to {brand}
              </button>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 capitalize">
              {brand} Promotions
            </h1>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="text-lg text-gray-600">Loading promotions...</div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-red-500 text-lg mb-4">
              Failed to load promotions for {brand}.
            </div>
            <p className="text-gray-600">
              Please check if the promotion API is available for this brand.
            </p>
          </div>
        ) : promotions.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-600 text-lg">
              No promotions found for {brand}.
            </div>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {promotions.map((promotion) => (
              <div
                key={promotion.id}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
              >
                {promotion.image_url && (
                  <img
                    src={promotion.image_url}
                    alt={promotion.title}
                    className="w-full h-48 object-cover"
                  />
                )}
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {promotion.title}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {promotion.description}
                  </p>

                  {(promotion.start_date || promotion.end_date) && (
                    <div className="text-sm text-gray-500 mb-4">
                      {promotion.start_date && (
                        <div>Start: {new Date(promotion.start_date).toLocaleDateString()}</div>
                      )}
                      {promotion.end_date && (
                        <div>End: {new Date(promotion.end_date).toLocaleDateString()}</div>
                      )}
                    </div>
                  )}

                  {promotion.terms_conditions && (
                    <details className="mt-4">
                      <summary className="cursor-pointer text-blue-600 hover:text-blue-800 font-medium">
                        Terms & Conditions
                      </summary>
                      <div className="mt-2 text-sm text-gray-600">
                        {promotion.terms_conditions}
                      </div>
                    </details>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
