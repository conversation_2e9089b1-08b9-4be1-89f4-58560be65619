{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy-help": "node deploy.js", "export": "next build && next export"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "next": "15.3.3", "quill": "^2.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "sqlite3": "^5.1.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}