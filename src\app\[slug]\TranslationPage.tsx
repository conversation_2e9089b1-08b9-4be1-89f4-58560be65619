'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';

type Translation = {
  id: number;
  original_text: string;
  translate_text: string;
};

export default function TranslationPage() {
  const { slug } = useParams();
  const [data, setData] = useState<Translation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const brand = Array.isArray(slug) ? slug[0].toLowerCase() : slug?.toLowerCase();
  console.log(brand);

  useEffect(() => {
    if (!brand) return;

    const fetchData = async () => {
      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-translation?brand=${brand}`
        );
        const result = await res.json();

        if (result.success && Array.isArray(result.data)) {
          setData(result.data);
        } else {
          setError(true);
        }
      } catch (err) {
        console.error('Fetch error:', err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [brand]);

  return (
    <div className="min-h-screen p-8 bg-gray-100">
      <h1 className="text-2xl font-bold mb-4">{brand}</h1>

      {loading ? (
        <p>Loading...</p>
      ) : error ? (
        <p className="text-red-500">Failed to load translations.</p>
      ) : data.length === 0 ? (
        <p>No translations found for {brand}.</p>
      ) : (
        data.map((item) => (
          <div key={item.id} className="bg-white p-4 rounded shadow mb-4">
            <div className="translation-content">
              <div
                className="font-semibold text-gray-700"
                dangerouslySetInnerHTML={{ __html: item.original_text }}
              />
              <div
                className="text-gray-800"
                dangerouslySetInnerHTML={{ __html: item.translate_text }}
              />
            </div>
          </div>
        ))
      )}
    </div>
  );
}
