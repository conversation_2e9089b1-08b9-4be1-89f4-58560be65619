'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';

type Translation = {
  id: number;
  original_text: string;
  translate_text: string;
};

export default function TranslationPage() {
  const { slug } = useParams();
  const router = useRouter();
  const [data, setData] = useState<Translation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const brand = Array.isArray(slug) ? slug[0].toLowerCase() : slug?.toLowerCase();
  console.log(brand);

  const handleViewPromotions = () => {
    router.push(`/${brand}/promotions`);
  };

  const handleBackToHome = () => {
    router.push('/');
  };

  useEffect(() => {
    if (!brand) return;

    const fetchData = async () => {
      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-translation?brand=${brand}`
        );
        const result = await res.json();

        if (result.success && Array.isArray(result.data)) {
          setData(result.data);
        } else {
          setError(true);
        }
      } catch (err) {
        console.error('Fetch error:', err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [brand]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackToHome}
                className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer"
              >
                ← Home
              </button>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 capitalize">
              {brand} Translations
            </h1>
            <button
              onClick={handleViewPromotions}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium cursor-pointer"
            >
              View Promotions
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

      {loading ? (
        <p>Loading...</p>
      ) : error ? (
        <p className="text-red-500">Failed to load translations.</p>
      ) : data.length === 0 ? (
        <p>No translations found for {brand}.</p>
      ) : (
        data.map((item) => (
          <div key={item.id} className="bg-white p-4 rounded shadow mb-4">
            <div className="translation-content">
              <div
                className="font-semibold text-gray-700"
                dangerouslySetInnerHTML={{ __html: item.original_text }}
              />
              <div
                className="text-gray-800"
                dangerouslySetInnerHTML={{ __html: item.translate_text }}
              />
            </div>
          </div>
        ))
      )}
      </div>
    </div>
  );
}
