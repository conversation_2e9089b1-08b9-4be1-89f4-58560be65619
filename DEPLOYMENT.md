# Deployment Guide

## Build Successfully Created! ✅

Your Next.js application has been built successfully. The build files are in the `.next` directory.

## Hosting Options

### Option 1: Vercel (Recommended - Easy)
1. Push your code to GitHub
2. Connect your GitHub repo to Vercel
3. Add environment variables in Vercel dashboard:
   - `DEEPL_API_KEY=your_deepl_api_key`
   - `DEEPL_GLOSSARY_ID=your_glossary_id` (optional)
4. Deploy automatically

### Option 2: Netlify
1. Push code to GitHub
2. Connect to Netlify
3. Build command: `npm run build`
4. Publish directory: `.next`
5. Add environment variables in Netlify dashboard

### Option 3: Traditional Server (VPS/Shared Hosting)
You need a server that supports Node.js:

1. Upload all files to your server
2. Install dependencies: `npm install`
3. Set environment variables in `.env.local`:
   ```
   DEEPL_API_KEY=your_deepl_api_key
   DEEPL_GLOSSARY_ID=your_glossary_id
   ```
4. Start the application: `npm start`

### Option 4: Static Export (for basic hosting)
If you only have basic hosting without Node.js support:

1. Change `next.config.ts` to:
   ```typescript
   const nextConfig: NextConfig = {
     output: 'export',
     trailingSlash: true,
     images: { unoptimized: true }
   };
   ```
2. Modify the API call to use client-side DeepL API
3. Run `npm run build` - this creates an `out` folder
4. Upload the `out` folder contents to your hosting

## Environment Variables Required

```
DEEPL_API_KEY=your_deepl_api_key_here
DEEPL_GLOSSARY_ID=your_glossary_id_here (optional)
```

## Files to Upload (for manual deployment)

### For Node.js hosting:
- All project files
- `.next` folder (build output)
- `package.json`
- `package-lock.json`

### For static hosting:
- Contents of `out` folder (after static export)

## Testing Your Deployment

1. Visit your domain
2. Test the translation functionality
3. Check `/api/test` endpoint to verify API is working
4. Monitor browser console for any errors

## Troubleshooting

If you get "Unexpected token '<'" error:
1. Check if environment variables are set correctly
2. Verify API routes are accessible
3. Check server logs for errors
4. Ensure your hosting supports Next.js API routes
