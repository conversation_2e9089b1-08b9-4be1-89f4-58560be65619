

import TranslationPage from './TranslationPage';

export async function generateStaticParams() {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/get-brands`, {
      cache: 'no-store',
    });
    const result = await res.json();

    if (result.success && Array.isArray(result.data)) {
      return result.data.map((brand: { name: string }) => ({
        slug: brand.name, // Using the brand name (e.g., "Mistycasino") as slug
      }));
    }
  } catch (error) {
    console.error('Error fetching brands for static params:', error);
  }
}

export default function Page() {
  return <TranslationPage />;
}
