import { useCallback } from 'react';
import { TranslationHistoryItem, LanguagePair } from '@/types/translation';
import { sourceLanguages, targetLanguages } from '@/lib/languages';
import { useLocalStorage } from './useLocalStorage';

export function useTranslationHistory() {
  const [history, setHistory] = useLocalStorage<TranslationHistoryItem[]>('translation-history', []);

  const addTranslation = useCallback((
    originalText: string,
    translatedText: string,
    sourceLang: string,
    targetLang: string,
    detectedSourceLang?: string,
    glossaryUsed?: boolean,
    glossaryMessage?: string
  ) => {
    const newTranslation: TranslationHistoryItem = {
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      originalText,
      translatedText,
      sourceLang,
      targetLang,
      timestamp: new Date(),
      detectedSourceLang,
      glossaryUsed,
      glossaryMessage
    };

    setHistory(prev => [newTranslation, ...prev]);
    return newTranslation;
  }, [setHistory]);

  const deleteTranslation = useCallback((id: string) => {
    setHistory(prev => prev.filter(item => item.id !== id));
  }, [setHistory]);

  const getTranslationsByLanguagePair = useCallback((sourceLang: string, targetLang: string) => {
    return history.filter(item => item.sourceLang === sourceLang && item.targetLang === targetLang);
  }, [history]);

  const getLanguagePairs = useCallback((): LanguagePair[] => {
    const pairs: { [key: string]: LanguagePair } = {};

    history.forEach(item => {
      const key = `${item.sourceLang}-${item.targetLang}`;
      if (!pairs[key]) {
        const sourceLangName = sourceLanguages.find(lang => lang.code === item.sourceLang)?.name || item.sourceLang;
        const targetLangName = targetLanguages.find(lang => lang.code === item.targetLang)?.name || item.targetLang;

        pairs[key] = {
          id: key,
          sourceLang: item.sourceLang,
          targetLang: item.targetLang,
          sourceLanguageName: sourceLangName,
          targetLanguageName: targetLangName,
          count: 0
        };
      }
      pairs[key].count++;
    });

    return Object.values(pairs).sort((a, b) => b.count - a.count);
  }, [history]);

  const clearHistory = useCallback(() => {
    setHistory([]);
  }, [setHistory]);

  return {
    history,
    addTranslation,
    deleteTranslation,
    getTranslationsByLanguagePair,
    getLanguagePairs,
    clearHistory
  };
}
