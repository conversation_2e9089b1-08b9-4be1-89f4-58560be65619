

'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

type Brand = {
  name: string; // changed from `slug` to match your API
};

export default function Home() {
  const router = useRouter();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(true);

  const handleBrandSelect = (brandName: string) => {
    if (brandName !== 'all') {
      router.push(`/${brandName}`);
    }
  };

  const handlePromotionSelect = (brandName: string) => {
    if (brandName !== 'all') {
      router.push(`/${brandName}/promotions`);
    }
  };

  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/get-brands`);
        const result = await res.json();

        if (result.success && Array.isArray(result.data)) {
          setBrands(result.data);
        }
      } catch (error) {
        console.error('Error fetching brands:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBrands();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <h1 className="text-2xl font-bold text-gray-900">Choose Brand</h1>
          </div>
        </div>
      </div>

      {/* Brand and Promotions Dropdowns */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center space-x-8">
            {/* Brand Dropdown */}
            <div className="w-64">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Brand
              </label>
              <select
                onChange={(e) => handleBrandSelect(e.target.value)}
                defaultValue="all"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all" disabled>
                  {loading ? 'Loading Brands...' : 'Select Brand'}
                </option>
                {brands.map((brand) => (
                  <option key={brand.name} value={brand.name}>
                    {brand.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Promotions Dropdown */}
            <div className="w-64">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Promotions
              </label>
              <select
                onChange={(e) => handlePromotionSelect(e.target.value)}
                defaultValue="all"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all" disabled>
                  {loading ? 'Loading Brands...' : 'Select Brand for Promotions'}
                </option>
                {brands.map((brand) => (
                  <option key={`${brand.name}-promotion`} value={brand.name}>
                    {brand.name}-promotion
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
