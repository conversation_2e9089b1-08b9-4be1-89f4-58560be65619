

'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

type Brand = {
  name: string; // changed from `slug` to match your API
};

export default function Home() {
  const router = useRouter();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(true);

  const handleBrandSelect = (brandName: string) => {
    if (brandName !== 'all') {
      router.push(`/${brandName}`);
    }
  };

  const handlePromotionSelect = (brandName: string) => {
    if (brandName !== 'all') {
      router.push(`/${brandName}/promotions`);
    }
  };

  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/get-brands`);
        const result = await res.json();

        if (result.success && Array.isArray(result.data)) {
          setBrands(result.data);
        }
      } catch (error) {
        console.error('Error fetching brands:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBrands();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center h-20">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-1">Brand Dashboard</h1>
              <p className="text-sm text-gray-600">Select a brand to view translations or promotions</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Brand and Promotions Cards */}
          <div className="grid md:grid-cols-2 gap-8">
            {/* Brand Selection Card */}
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8 hover:shadow-xl transition-shadow duration-300">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Brand Translations</h2>
                <p className="text-gray-600 text-sm">Access translation management for your brand</p>
              </div>

              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700">
                  Select Brand
                </label>
                <select
                  onChange={(e) => handleBrandSelect(e.target.value)}
                  defaultValue="all"
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 cursor-pointer hover:border-gray-400 transition-colors duration-200 text-gray-900"
                >
                  <option value="all" disabled className="text-gray-500">
                    {loading ? 'Loading Brands...' : 'Choose a brand'}
                  </option>
                  {brands.map((brand) => (
                    <option key={brand.name} value={brand.name} className="text-gray-900">
                      {brand.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Promotions Selection Card */}
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8 hover:shadow-xl transition-shadow duration-300">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Brand Promotions</h2>
                <p className="text-gray-600 text-sm">View and manage promotional campaigns</p>
              </div>

              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700">
                  Select Brand for Promotions
                </label>
                <select
                  onChange={(e) => handlePromotionSelect(e.target.value)}
                  defaultValue="all"
                  className="block w-full px-4 py-3 border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 cursor-pointer hover:border-gray-400 transition-colors duration-200 text-gray-900"
                >
                  <option value="all" disabled className="text-gray-500">
                    {loading ? 'Loading Brands...' : 'Choose a brand'}
                  </option>
                  {brands.map((brand) => (
                    <option key={`${brand.name}-promotion`} value={brand.name} className="text-gray-900">
                      {brand.name} Promotions
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="text-center mt-12">
              <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading brands...
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
