export interface TranslationRequest {
  text: string;
  source_lang: string;
  target_lang: string;
  glossary_id?: string;
}

export interface TranslationResponse {
  translations: Array<{
    detected_source_language: string;
    text: string;
  }>;
  glossaryUsed?: boolean;
  glossaryMessage?: string;
}

export interface Language {
  code: string;
  name: string;
}

export interface TranslationError {
  error: string;
  message: string;
}

// Translation History Types
export interface TranslationHistoryItem {
  id: string;
  originalText: string;
  translatedText: string;
  sourceLang: string;
  targetLang: string;
  timestamp: Date;
  detectedSourceLang?: string;
  glossaryUsed?: boolean;
  glossaryMessage?: string;
}

export interface LanguagePair {
  id: string;
  sourceLang: string;
  targetLang: string;
  sourceLanguageName: string;
  targetLanguageName: string;
  count: number;
}

export interface HistoryStats {
  totalTranslations: number;
  languagePairsCount: number;
  languagePairs: { [key: string]: number };
  recentActivity: TranslationHistoryItem[];
}
